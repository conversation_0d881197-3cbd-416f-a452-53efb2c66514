# Loki + Promtail + <PERSON><PERSON> (Local)

로컬에서 **<PERSON>ana Loki**를 빠르게 테스트하기 위한 Docker Compose 템플릿입니다.
Windows 11 (Docker Desktop), macOS, Linux에서 동작합니다.

## 구성
- **Loki**: 로그 저장/검색
- **Promtail**: 파일 로그 수집기
- **Grafana**: 대시보드 & LogQL 탐색기
- **프로비저닝**: Grafana에 Loki 데이터소스 자동 등록

## 포트
- <PERSON>ana: <http://localhost:3000> (admin / admin)
- Loki API: <http://localhost:3100>

## 사용 방법

1) 저장소 다운로드 후 디렉토리로 이동
```bash
cd loki-local
```

2) 샘플 로그 생성 (선택)
```bash
# PowerShell (Windows)
1..5 | ForEach-Object { "$((Get-Date).ToString('s')) INFO Sample log line $_" } >> .\logs\sample.log

# macOS/Linux (bash/zsh)
for i in {1..5}; do echo "$(date -Is) INFO Sample log line $i" >> ./logs/sample.log; done
```

3) 컨테이너 실행
```bash
docker compose up -d
```

4) Grafana 접속
- URL: <http://localhost:3000>
- ID/PW: `admin` / `admin`

5) 로그 탐색 (Explore → Loki 선택)
- 아래와 같이 쿼리 가능
```logql
{job="app", app="sample"}  |~ "INFO"
```
또는 최신 로그
```logql
{job="app"} |= "Sample"
```

6) 실시간 로그 보기 (tail)
```logql
{job="app"} | logfmt | line_format "{{.ts}} {{.level}} {{.msg}}"
```

7) 추가 로그 계속 쓰기
```bash
# PowerShell
1..100 | ForEach-Object { Start-Sleep -Milliseconds 500; "$((Get-Date).ToString('s')) INFO streaming line $_" >> .\logs\sample.log }

# macOS/Linux
for i in {1..100}; do echo "$(date -Is) INFO streaming line $i" >> ./logs/sample.log; sleep 0.5; done
```

## 파일 구조
```
loki-local/
├─ docker-compose.yml
├─ loki-config.yaml
├─ promtail-config.yaml
├─ grafana/
│  └─ provisioning/
│     └─ datasources/
│        └─ datasource.yaml
└─ logs/
   └─ sample.log  (샘플 로그 파일, 없어도 됨)
```

## 자주 묻는 질문(FAQ)

### Q1) Windows에서 /var/log는 어떻게 처리하나요?
이 템플릿은 로컬 `./logs/*.log`만 수집합니다. Windows 경로를 그대로 마운트해 사용하세요.

### Q2) 컨테이너 로그를 Loki로 보내고 싶어요.
도커 데몬 로그 수집은 Fluent Bit/Vector/Promtail Docker Plugin 등 별도 구성이 필요합니다. 간단히 테스트할 때는 애플리케이션 로그 파일을 `./logs`에 쓰는 방식이 가장 빠릅니다.

### Q3) 데이터 유지
볼륨(`loki_data`, `grafana_data`)에 저장됩니다. 초기화하려면 컨테이너 중지 후 볼륨을 삭제하세요.
```bash
docker compose down -v
```

### Q4) Promtail가 파일을 못 읽어요.
권한 또는 경로 문제일 수 있습니다. `./logs` 디렉토리와 파일이 실제로 존재하는지 확인하고, 컨테이너가 볼 수 있도록 compose의 볼륨 마운트를 점검하세요.

---

행운을 빕니다! 🚀
